{"version": 3, "file": "backend-impl.js", "sourceRoot": "", "sources": ["../../lib/backend-impl.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAelC,MAAM,QAAQ,GAA6B,IAAI,GAAG,EAAE,CAAC;AACrD,MAAM,wBAAwB,GAAa,EAAE,CAAC;AAE9C;;;;;;;;;GASG;AACI,MAAM,eAAe,GAAG,CAAC,IAAY,EAAE,OAAgB,EAAE,QAAgB,EAAQ,EAAE;IACxF,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,OAAO,CAAC,6BAA6B,KAAK,UAAU,EAAE;QAChH,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;SAC3C;aAAM,IAAI,cAAc,CAAC,QAAQ,GAAG,QAAQ,EAAE;YAC7C,8EAA8E;YAC9E,OAAO;SACR;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC/C,IAAI,cAAc,CAAC,OAAO,KAAK,OAAO,EAAE;gBACtC,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,oBAAoB,QAAQ,EAAE,CAAC,CAAC;aACjF;SACF;QAED,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,MAAM,CAAC,GAAG,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACZ,wBAAwB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACvC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxD,IAAI,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAE,CAAC,QAAQ,IAAI,QAAQ,EAAE;oBACnE,wBAAwB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC5C,OAAO;iBACR;aACF;YACD,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QACD,OAAO;KACR;IAED,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;AAC7C,CAAC,CAAC;AAhCW,QAAA,eAAe,mBAgC1B;AAEF;;;;;GAKG;AACH,MAAM,8BAA8B,GAAG,KAAK,EAAE,WAAmB,EAA6B,EAAE;IAC9F,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,oBAAoB,CAAC;KAC7B;IAED,IAAI,WAAW,CAAC,WAAW,EAAE;QAC3B,OAAO,WAAW,CAAC,OAAO,CAAC;KAC5B;SAAM,IAAI,WAAW,CAAC,OAAO,EAAE;QAC9B,OAAO,WAAW,CAAC,KAAM,CAAC;KAC3B;SAAM;QACL,MAAM,cAAc,GAAG,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC;QACjD,IAAI;YACF,IAAI,CAAC,cAAc,EAAE;gBACnB,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACjE;YACD,MAAM,WAAW,CAAC,WAAW,CAAC;YAC9B,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;YAC/B,OAAO,WAAW,CAAC,OAAO,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,cAAc,EAAE;gBACnB,WAAW,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC;gBAC3B,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;aAC5B;YACD,OAAO,WAAW,CAAC,KAAM,CAAC;SAC3B;gBAAS;YACR,OAAO,WAAW,CAAC,WAAW,CAAC;SAChC;KACF;AACH,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACI,MAAM,mCAAmC,GAAG,KAAK,EACtD,OAAwC,EAC+B,EAAE;IACzE,6CAA6C;IAC7C,MAAM,GAAG,GAAG,OAAO,CAAC,kBAAkB,IAAI,EAAE,CAAC;IAC7C,MAAM,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1E,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,YAAY,CAAC;IAEzF,uDAAuD;IACvD,IAAI,OAA4B,CAAC;IACjC,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAU,CAAC;IAChD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;QACtC,MAAM,aAAa,GAAG,MAAM,8BAA8B,CAAC,WAAW,CAAC,CAAC;QACxE,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,aAAa,EAAE,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,GAAG,aAAa,CAAC;aACzB;YACD,IAAI,OAAO,KAAK,aAAa,EAAE;gBAC7B,qBAAqB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACxC;SACF;KACF;IAED,2CAA2C;IAC3C,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,oCAAoC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7G;IAED,wFAAwF;IACxF,KAAK,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,MAAM,EAAE;QAClC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC/B,sCAAsC;YACtC,OAAO,CAAC,IAAI,CACV,0CAA0C,IAAI,uDAAuD,GAAG,EAAE,CAC3G,CAAC;SACH;KACF;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAErG,OAAO;QACL,OAAO;QACP,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACpB,IAAI,IAAI,KAAK,oBAAoB,EAAE;oBACjC,OAAO,WAAW,CAAC;iBACpB;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACnC,CAAC;SACF,CAAC;KACH,CAAC;AACJ,CAAC,CAAC;AAtDW,QAAA,mCAAmC,uCAsD9C"}