/**
 * @ignore
 */
export declare const TRACE: (deviceType: string, label: string) => void;
/**
 * @ignore
 */
export declare const TRACE_FUNC_BEGIN: (extraMsg?: string) => void;
/**
 * @ignore
 */
export declare const TRACE_FUNC_END: (extraMsg?: string) => void;
/**
 * @ignore
 */
export declare const TRACE_EVENT_BEGIN: (extraMsg?: string) => void;
/**
 * @ignore
 */
export declare const TRACE_EVENT_END: (extraMsg?: string) => void;
//# sourceMappingURL=trace.d.ts.map