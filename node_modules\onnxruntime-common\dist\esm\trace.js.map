{"version": 3, "file": "trace.js", "sourceRoot": "", "sources": ["../../lib/trace.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAElC,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AAEpC;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,UAAkB,EAAE,KAAa,EAAE,EAAE;IACzD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU,UAAU,KAAK,EAAE,CAAC,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,QAAiB,EAAE,EAAE;IACpD,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC5D,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACpD,IAAI,KAAK,GAAG,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,IAAI,QAAQ,EAAE;gBACZ,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC;aAC1B;YACD,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACpB,OAAO;SACR;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACnC,YAAY,GAAG,IAAI,CAAC;SACrB;KACF;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,QAAiB,EAAE,EAAE;IACpD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,QAAiB,EAAE,EAAE;IAClD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,QAAiB,EAAE,EAAE;IACrD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,IAAI,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,QAAiB,EAAE,EAAE;IACnD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,OAAO,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;AACtC,CAAC,CAAC"}