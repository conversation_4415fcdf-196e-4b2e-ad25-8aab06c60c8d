import { Tensor } from './tensor.js';
export type SupportedTypedArrayConstructors = Float32ArrayConstructor | Uint8ArrayConstructor | Int8ArrayConstructor | Uint16ArrayConstructor | Int16ArrayConstructor | Int32ArrayConstructor | BigInt64ArrayConstructor | Uint8ArrayConstructor | Float64ArrayConstructor | Uint32ArrayConstructor | BigUint64ArrayConstructor;
export type SupportedTypedArray = InstanceType<SupportedTypedArrayConstructors>;
export declare const NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP: Map<string, SupportedTypedArrayConstructors>;
export declare const NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP: Map<SupportedTypedArrayConstructors, keyof Tensor.DataTypeMap>;
export declare const checkTypedArray: () => void;
//# sourceMappingURL=tensor-impl-type-mapping.d.ts.map