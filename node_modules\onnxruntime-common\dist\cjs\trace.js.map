{"version": 3, "file": "trace.js", "sourceRoot": "", "sources": ["../../lib/trace.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,+CAAoC;AAEpC;;GAEG;AACI,MAAM,KAAK,GAAG,CAAC,UAAkB,EAAE,KAAa,EAAE,EAAE;IACzD,IAAI,OAAO,iBAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU,UAAU,KAAK,EAAE,CAAC,CAAC;AACpD,CAAC,CAAC;AANW,QAAA,KAAK,SAMhB;AAEF,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,QAAiB,EAAE,EAAE;IACpD,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC5D,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACpD,IAAI,KAAK,GAAG,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,IAAI,QAAQ,EAAE;gBACZ,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC;aAC1B;YACD,IAAA,aAAK,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACpB,OAAO;SACR;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACnC,YAAY,GAAG,IAAI,CAAC;SACrB;KACF;AACH,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,gBAAgB,GAAG,CAAC,QAAiB,EAAE,EAAE;IACpD,IAAI,OAAO,iBAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAChC,CAAC,CAAC;AALW,QAAA,gBAAgB,oBAK3B;AAEF;;GAEG;AACI,MAAM,cAAc,GAAG,CAAC,QAAiB,EAAE,EAAE;IAClD,IAAI,OAAO,iBAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC9B,CAAC,CAAC;AALW,QAAA,cAAc,kBAKzB;AAEF;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAC,QAAiB,EAAE,EAAE;IACrD,IAAI,OAAO,iBAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,IAAI,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;AACnC,CAAC,CAAC;AANW,QAAA,iBAAiB,qBAM5B;AAEF;;GAEG;AACI,MAAM,eAAe,GAAG,CAAC,QAAiB,EAAE,EAAE;IACnD,IAAI,OAAO,iBAAG,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAG,CAAC,KAAK,EAAE;QACnE,OAAO;KACR;IACD,sCAAsC;IACtC,OAAO,CAAC,OAAO,CAAC,QAAQ,QAAQ,EAAE,CAAC,CAAC;AACtC,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B"}