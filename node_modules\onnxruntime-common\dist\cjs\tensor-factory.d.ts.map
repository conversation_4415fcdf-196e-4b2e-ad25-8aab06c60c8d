{"version": 3, "file": "tensor-factory.d.ts", "sourceRoot": "", "sources": ["../../lib/tensor-factory.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAElD,MAAM,MAAM,WAAW,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC;AACzD,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG,MAAM,CAAC;AAMhD;;GAEG;AACH,UAAU,2BAA2B,CAAC,CAAC,CAAE,SAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;IACnE;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;CAClB;AAED;;GAEG;AACH,UAAU,gCAAgC,CAAC,CAAC,SAAS,MAAM,CAAC,IAAI;IAC9D;;;;OAIG;IACH,QAAQ,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5C;;;;OAIG;IACH,OAAO,CAAC,IAAI,IAAI,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,8BAA8B,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAC7G,SAAQ,2BAA2B,CAAC,CAAC,CAAC;IACtC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;IAChC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B,CAAC,CAAC,SAAS,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CACvG,SAAQ,2BAA2B,CAAC,CAAC,CAAC,EACpC,gCAAgC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC7B;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,8BAA8B,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAC7G,SAAQ,2BAA2B,CAAC,CAAC,CAAC,EACpC,gCAAgC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;IAChC;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC;CAC1C;AAED,MAAM,WAAW,6BAA6B,CAAC,CAAC,SAAS,MAAM,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAC1G,SAAQ,2BAA2B,CAAC,CAAC,CAAC,EACpC,gCAAgC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC;IAE/B;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC;CACxC;AASD,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAED,MAAM,WAAW,mBAAmB;IAClC;;;;;OAKG;IACH,YAAY,CAAC,EAAE,WAAW,CAAC;CAC5B;AAED,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,GAAG,OAAO,CAAC;CAChC;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,YAAY,CAAC,EAAE,iBAAiB,CAAC;CAClC;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,8BAA8B;IAC7C;;;;OAIG;IACH,IAAI,CAAC,EAAE;QACL;;;;;;WAMG;QACH,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC5E;;;;;;WAMG;QACH,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAC7E,CAAC;CACH;AAMD,MAAM,WAAW,0BACf,SAAQ,uBAAuB,EAC7B,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,8BAA8B;CAAG;AAErC,MAAM,WAAW,6BACf,SAAQ,uBAAuB,EAC7B,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,8BAA8B;CAAG;AAErC,MAAM,WAAW,oBACf,SAAQ,iBAAiB,EACvB,uBAAuB,EACvB,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,8BAA8B;CAAG;AAErC,MAAM,WAAW,4BACf,SAAQ,uBAAuB,EAC7B,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,8BAA8B;CAAG;AAErC,MAAM,WAAW,wBAAwB,CAAC,CAAC,SAAS,MAAM,CAAC,gBAAgB,CACzE,SAAQ,QAAQ,CAAC,iBAAiB,CAAC,EACjC,aAAa,EACb,gCAAgC,CAAC,CAAC,CAAC;CAAwB;AAE/D,MAAM,WAAW,0BAA0B,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,CAC7E,SAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAC1B,gCAAgC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,CAAC;CACd;AAED,MAAM,WAAW,yBAAyB,CAAC,CAAC,SAAS,MAAM,CAAC,iBAAiB,CAC3E,SAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAC1B,gCAAgC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,CAAC;CACd;AAID;;;GAGG;AACH,MAAM,WAAW,aAAa;IAC5B;;;;;;;;;;;OAWG;IACH,SAAS,CACP,SAAS,EAAE,SAAS,EACpB,OAAO,CAAC,EAAE,0BAA0B,GACnC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAE1D;;;;;;;;;;;OAWG;IACH,SAAS,CACP,YAAY,EAAE,gBAAgB,EAC9B,OAAO,CAAC,EAAE,6BAA6B,GACtC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAE1D;;;;;;;;;;;OAWG;IACH,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAErH;;;;;;;;;;;OAWG;IACH,SAAS,CACP,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,4BAA4B,GACpC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAE1D;;;;;;;;;;;;;;;;;OAiBG;IACH,WAAW,CAAC,CAAC,SAAS,MAAM,CAAC,gBAAgB,GAAG,SAAS,EACvD,OAAO,EAAE,MAAM,CAAC,WAAW,EAC3B,OAAO,EAAE,wBAAwB,CAAC,CAAC,CAAC,GACnC,WAAW,CAAC,SAAS,CAAC,CAAC;IAE1B;;;;;;;;;;;;;;;;OAgBG;IACH,aAAa,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,EAC/C,MAAM,EAAE,MAAM,CAAC,aAAa,EAC5B,OAAO,EAAE,0BAA0B,CAAC,CAAC,CAAC,GACrC,WAAW,CAAC,CAAC,CAAC,CAAC;IAElB;;;;;;;;;;;;;;;;;OAiBG;IACH,YAAY,CAAC,CAAC,SAAS,MAAM,CAAC,iBAAiB,EAC7C,MAAM,EAAE,MAAM,CAAC,YAAY,EAC3B,OAAO,EAAE,yBAAyB,CAAC,CAAC,CAAC,GACpC,WAAW,CAAC,CAAC,CAAC,CAAC;IAElB;;;;;;;;OAQG;IACH,gBAAgB,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,EACvD,IAAI,EAAE,CAAC,EACP,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAC7B,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,WAAW,CAAC,CAAC,CAAC,CAAC;CACnB"}