{"version": 3, "file": "inference-session-impl.js", "sourceRoot": "", "sources": ["../../lib/inference-session-impl.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAElC,OAAO,EAAE,mCAAmC,EAAE,MAAM,mBAAmB,CAAC;AAIxE,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAQlG,MAAM,OAAO,gBAAgB;IAC3B,YAAoB,OAAgC;QAClD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAGD,KAAK,CAAC,GAAG,CAAC,KAAgB,EAAE,IAA+B,EAAE,IAAiB;QAC5E,gBAAgB,EAAE,CAAC;QACnB,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAyC,EAAE,CAAC;QACzD,IAAI,OAAO,GAAe,EAAE,CAAC;QAC7B,eAAe;QACf,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,YAAY,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAClG,MAAM,IAAI,SAAS,CACjB,+FAA+F,CAChG,CAAC;SACH;QAED,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,yCAAyC;QACzC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;aAChE;YACD,IAAI,IAAI,YAAY,MAAM,EAAE;gBAC1B,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;aACrD;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;iBAC5D;gBACD,cAAc,GAAG,KAAK,CAAC;gBACvB,eAAe;gBACf,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;oBACvB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;wBAC5B,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;qBACvE;oBACD,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBACzC,MAAM,IAAI,UAAU,CAAC,2CAA2C,IAAI,GAAG,CAAC,CAAC;qBAC1E;oBACD,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;iBACtB;gBAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;oBAC7C,OAAO,GAAG,IAAI,CAAC;iBAChB;qBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;oBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;iBACrD;aACF;iBAAM;gBACL,4CAA4C;gBAC5C,yFAAyF;gBACzF,IAAI,SAAS,GAAG,KAAK,CAAC;gBACtB,MAAM,QAAQ,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAClD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;oBACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBACjC,MAAM,CAAC,GAAI,IAA2D,CAAC,IAAI,CAAC,CAAC;wBAC7E,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,MAAM,EAAE;4BACrC,SAAS,GAAG,IAAI,CAAC;4BACjB,cAAc,GAAG,KAAK,CAAC;4BACvB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;qBACF;iBACF;gBAED,IAAI,SAAS,EAAE;oBACb,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;wBAC7C,OAAO,GAAG,IAAI,CAAC;qBAChB;yBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;wBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;qBACrD;iBACF;qBAAM;oBACL,OAAO,GAAG,IAAkB,CAAC;iBAC9B;aACF;SACF;aAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YACtC,MAAM,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;SAChF;QAED,kCAAkC;QAClC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YAClC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;gBACtC,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,0BAA0B,CAAC,CAAC;aAC3D;SACF;QAED,gEAAgE;QAChE,IAAI,cAAc,EAAE;YAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;gBACnC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;aACtB;SACF;QAED,0CAA0C;QAE1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAChE,MAAM,WAAW,GAAkC,EAAE,CAAC;QACtD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC5B,IAAI,MAAM,YAAY,MAAM,EAAE;oBAC5B,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;iBAC3B;qBAAM;oBACL,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;iBACtE;aACF;SACF;QACD,eAAe,CAAC,sBAAsB,CAAC,CAAC;QACxC,cAAc,EAAE,CAAC;QACjB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAWD,MAAM,CAAC,KAAK,CAAC,MAAM,CACjB,IAA2C,EAC3C,IAA8B,EAC9B,IAAa,EACb,IAAqB;QAErB,gBAAgB,EAAE,CAAC;QACnB,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;QAC7C,oCAAoC;QACpC,IAAI,oBAAyC,CAAC;QAC9C,IAAI,OAAO,GAAmB,EAAE,CAAC;QAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,oBAAoB,GAAG,IAAI,CAAC;YAC5B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;gBAC7C,OAAO,GAAG,IAAI,CAAC;aAChB;iBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;aACrD;SACF;aAAM,IAAI,IAAI,YAAY,UAAU,EAAE;YACrC,oBAAoB,GAAG,IAAI,CAAC;YAC5B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;gBAC7C,OAAO,GAAG,IAAI,CAAC;aAChB;iBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;aACrD;SACF;aAAM,IACL,IAAI,YAAY,WAAW;YAC3B,CAAC,OAAO,iBAAiB,KAAK,WAAW,IAAI,IAAI,YAAY,iBAAiB,CAAC,EAC/E;YACA,MAAM,MAAM,GAAG,IAAI,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACjC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;gBAC7C,OAAO,GAAG,IAAI,CAAC;aAChB;iBAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACnC,UAAU,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;oBACrC,MAAM,IAAI,UAAU,CAAC,kCAAkC,CAAC,CAAC;iBAC1D;gBACD,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,MAAM,CAAC,UAAU,EAAE;oBACrD,MAAM,IAAI,UAAU,CAAC,oCAAoC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;iBACjF;gBACD,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;gBAC1C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,UAAU,GAAG,IAAI,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;wBACrC,MAAM,IAAI,UAAU,CAAC,kCAAkC,CAAC,CAAC;qBAC1D;oBACD,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,GAAG,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE;wBAClE,MAAM,IAAI,UAAU,CAAC,oCAAoC,MAAM,CAAC,UAAU,GAAG,UAAU,IAAI,CAAC,CAAC;qBAC9F;oBACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;wBAC7C,OAAO,GAAG,IAAI,CAAC;qBAChB;yBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;wBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;qBACrD;iBACF;qBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;oBACtC,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAC;iBACvD;aACF;iBAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBACtC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;aACrD;YACD,oBAAoB,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;SACvE;aAAM;YACL,MAAM,IAAI,SAAS,CAAC,qDAAqD,CAAC,CAAC;SAC5E;QAED,yFAAyF;QACzF,MAAM,CAAC,OAAO,EAAE,uBAAuB,CAAC,GAAG,MAAM,mCAAmC,CAAC,OAAO,CAAC,CAAC;QAC9F,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,6BAA6B,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,CAAC;QAC3G,eAAe,CAAC,yBAAyB,CAAC,CAAC;QAC3C,cAAc,EAAE,CAAC;QACjB,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IAChC,CAAC;IACD,YAAY;QACV,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IAC9B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IACjC,CAAC;IACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IAClC,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IACpC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;IACrC,CAAC;CAGF"}