{"version": 3, "file": "tensor-conversion-impl.js", "sourceRoot": "", "sources": ["../../lib/tensor-conversion-impl.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAKlC;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,MAAc,EAAE,OAAgC,EAAU,EAAE;IAC1F,MAAM,MAAM,GAAG,OAAO,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9G,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAGtC,CAAC;IAET,IAAI,eAAe,IAAI,IAAI,EAAE;QAC3B,+CAA+C;QAC/C,IAAI,KAAa,CAAC;QAClB,IAAI,MAAc,CAAC;QACnB,IAAI,OAAO,EAAE,YAAY,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;YAC1E,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACzB;aAAM;YACL,yBAAyB;YACzB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACzB;QAED,MAAM,WAAW,GAAG,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QAE3E,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;QAC3B,IAAI,QAA0C,CAAC;QAC/C,IAAI,QAA0C,CAAC;QAC/C,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aACzD;iBAAM;gBACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzD,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC9B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACF;SACF;QACD,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aACzD;iBAAM;gBACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzD,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC9B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACF;SACF;QAED,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC9B,8BAA8B;QAC9B,IAAI,cAAc,GAAG,CAAC,EACpB,cAAc,GAAG,MAAM,EACvB,cAAc,GAAG,MAAM,GAAG,CAAC,EAC3B,cAAc,GAAG,CAAC,CAAC,CAAC;QAEtB,mEAAmE;QACnE,IAAI,WAAW,KAAK,MAAM,EAAE;YAC1B,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;YAC5B,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,WAAW,KAAK,KAAK,EAAE;YAChC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,WAAW,KAAK,KAAK,EAAE;YAChC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9B,MAAM,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC7F,MAAM,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC7F,MAAM,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC7F,MAAM,CAAC,GAAG,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC3H,qEAAqE;gBACrE,eAAe,CAAC,SAAS,GAAG,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBAC5E,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtC;SACF;QACD,IAAI,WAAW,IAAI,MAAM,EAAE;YACzB,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;SAC3B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;KACF;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,MAAc,EAAE,OAAkC,EAAa,EAAE;IACjG,MAAM,eAAe,GACnB,OAAO,QAAQ,KAAK,WAAW;QAC7B,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;QACnD,CAAC,CAAE,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAuC,CAAC;IACxF,IAAI,KAAgB,CAAC;IACrB,IAAI,eAAe,IAAI,IAAI,EAAE;QAC3B,+CAA+C;QAC/C,IAAI,KAAa,CAAC;QAClB,IAAI,MAAc,CAAC;QACnB,IAAI,QAAgB,CAAC;QACrB,IAAI,OAAO,EAAE,YAAY,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;YAC1E,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC3B;aAAM;YACL,yBAAyB;YACzB,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC3B;QACD,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAE5G,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;QAC3B,IAAI,QAA0C,CAAC;QAC/C,IAAI,QAA0C,CAAC;QAC/C,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aACzD;iBAAM;gBACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC3D,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC9B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACF;SACF;QACD,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aACzD;iBAAM;gBACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzD,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC9B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACF;SACF;QAED,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC9B,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IACE,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;gBAC7E,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,EACxE;gBACA,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;aAClE;SACF;QAED,8BAA8B;QAC9B,MAAM,IAAI,GAAG,CAAC,CAAC;QACf,IAAI,aAAa,GAAG,CAAC,EACnB,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,CAAC,CAAC;QACpB,IAAI,cAAc,GAAG,CAAC,EACpB,cAAc,GAAG,MAAM,EACvB,cAAc,GAAG,MAAM,GAAG,CAAC,EAC3B,cAAc,GAAG,CAAC,CAAC,CAAC;QAEtB,mEAAmE;QACnE,IAAI,WAAW,KAAK,MAAM,EAAE;YAC1B,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;YAC5B,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,WAAW,KAAK,KAAK,EAAE;YAChC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,WAAW,KAAK,KAAK,EAAE;YAChC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;QAED,KAAK,GAAG,eAAe,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEvD,KACE,IAAI,CAAC,GAAG,CAAC,EACT,CAAC,GAAG,MAAM,GAAG,KAAK,EAClB,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,CAAC,EAAE,EAC/F;YACA,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;YAC/G,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;YAC/G,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;YAC/G,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;gBACvB,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;SACpH;KACF;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}