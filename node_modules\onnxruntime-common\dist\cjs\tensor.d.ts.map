{"version": 3, "file": "tensor.d.ts", "sourceRoot": "", "sources": ["../../lib/tensor.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AAIpD;;GAEG;AACH,UAAU,eAAe,CAAC,CAAC,SAAS,MAAM,CAAC,IAAI;IAC7C;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,SAAS,MAAM,EAAE,CAAC;IACjC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACjB;;;;OAIG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC;IACvC;;;;OAIG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC;IACrC;;;;OAIG;IACH,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC;IAEzC;;;;OAIG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC;IAEvC;;;;;;;OAOG;IACH,OAAO,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/D;;;;;;;OAOG;IACH,OAAO,IAAI,IAAI,CAAC;CACjB;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,UAAU,WAAW;QACnB,OAAO,EAAE,YAAY,CAAC;QACtB,KAAK,EAAE,UAAU,CAAC;QAClB,IAAI,EAAE,SAAS,CAAC;QAChB,MAAM,EAAE,WAAW,CAAC;QACpB,KAAK,EAAE,UAAU,CAAC;QAClB,KAAK,EAAE,UAAU,CAAC;QAClB,KAAK,EAAE,aAAa,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,CAAC;QACjB,IAAI,EAAE,UAAU,CAAC;QACjB,OAAO,EAAE,WAAW,CAAC;QACrB,OAAO,EAAE,YAAY,CAAC;QACtB,MAAM,EAAE,WAAW,CAAC;QACpB,MAAM,EAAE,cAAc,CAAC;QAIvB,KAAK,EAAE,UAAU,CAAC;QAClB,IAAI,EAAE,SAAS,CAAC;KACjB;IAED,UAAU,cAAc;QACtB,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;QACf,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,OAAO,CAAC;QACd,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QAIf,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;KACd;IAED,KAAK,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,KAAK,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IAExC;;OAEG;IACH,KAAY,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAEhE;;OAEG;IACH,KAAY,WAAW,GAAG,YAAY,CAAC;IAEvC;;OAEG;IACH,KAAY,gBAAgB,GAAG,SAAS,CAAC;IAEzC,KAAK,qBAAqB,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAA;KAAE,CAAC;IAC3F;;OAEG;IACH,KAAY,aAAa,GAAG,gBAAgB,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;IAEjF,KAAK,oBAAoB,GAAG;QAAE,OAAO,IAAI,IAAI,CAAA;KAAE,CAAC;IAChD;;;;OAIG;IACH,KAAY,YAAY,GAAG,gBAAgB,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;IAE9E;;OAEG;IACH,KAAY,kBAAkB,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;IAEzG;;OAEG;IACH,KAAY,iBAAiB,GACzB,SAAS,GACT,SAAS,GACT,MAAM,GACN,OAAO,GACP,OAAO,GACP,QAAQ,GACR,OAAO,GACP,QAAQ,GACR,MAAM,GACN,OAAO,GACP,MAAM,CAAC;IAEX;;OAEG;IACH,KAAY,YAAY,GAAG,MAAM,GAAG,KAAK,GAAG,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,WAAW,CAAC;IAElG;;OAEG;IACH,KAAY,IAAI,GAAG,MAAM,WAAW,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,WAAW,CAAC,CAAC,SAAS,MAAM,CAAC,IAAI,CAAE,SAAQ,eAAe,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;CAAG;AACtG;;GAEG;AACH,MAAM,WAAW,MAAO,SAAQ,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;CAAG;AAE9F;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,aAAa;IAEtD;;;;;;OAMG;IACH,KACE,IAAI,EAAE,QAAQ,EACd,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,SAAS,MAAM,EAAE,EACtD,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,WAAW,CAAC,QAAQ,CAAC,CAAC;IAEzB;;;;;;OAMG;IACH,KACE,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,SAAS,OAAO,EAAE,EACrD,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,WAAW,CAAC,MAAM,CAAC,CAAC;IAEvB;;;;;;OAMG;IACH,KAAK,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAE7F;;;;;;OAMG;IACH,KAAK,CAAC,SAAS,QAAQ,GAAG,OAAO,EAC/B,IAAI,EAAE,CAAC,EACP,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,EACnE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,WAAW,CAAC,CAAC,CAAC,CAAC;IAElB;;;;;;OAMG;IACH,KAAK,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,EACzE,IAAI,EAAE,CAAC,EACP,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,SAAS,MAAM,EAAE,EAC/C,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,WAAW,CAAC,CAAC,CAAC,CAAC;IAKlB;;;;;OAKG;IACH,KAAK,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;IAE3E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAErE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAEvE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAE9E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAEzE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAEvE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAEvE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAE1E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,SAAS,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAE/E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,SAAS,OAAO,EAAE,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAE9E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;IAE3E;;;;;OAKG;IACH,KAAK,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAEzE;;;;;OAKG;IACH,KAAK,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAM5E;;;;;;OAMG;IACH,KACE,IAAI,EAAE,MAAM,CAAC,IAAI,EACjB,IAAI,EAAE,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,OAAO,EAAE,EACtG,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,MAAM,CAAC;IAEV;;;;;OAKG;IACH,KAAK,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,MAAM,CAAC;CAE/D;AAGD,eAAO,MAAM,MAAM,mBAAkC,CAAC"}