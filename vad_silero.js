// vad_silero.js
// 依赖：npm i onnxruntime-node
const ort = require('onnxruntime-node');

class SileroVAD {
  /**
   * @param {Object} opts
   * @param {number} opts.frameMs  帧长(ms)，推荐30
   * @param {number} opts.threshold 判定阈值(0~1)
   * @param {number} opts.startMs  连续>=startMs 才算开始
   * @param {number} opts.endMs    连续>=endMs   才算结束
   * @param {number} opts.preRollMs 段开头额外前滚
   * @param {number} opts.postRollMs 段结尾额外后滚
   * @param {number} opts.minSpeechMs 极短段丢弃阈值
   * @param {number} opts.maxSegmentMs 超过强切
   */
  constructor (opts = {}) {
    this.sampleRate = 16000;
    this.frameMs = opts.frameMs ?? 30; // 30ms更稳
    this.frameLen = Math.round(this.sampleRate * this.frameMs / 1000); // 480 for 30ms
    this.threshold  = opts.threshold ?? 0.5;

    this.nStart = Math.ceil((opts.startMs ?? 60)  / this.frameMs);
    this.nEnd   = Math.ceil((opts.endMs   ?? 200) / this.frameMs);
    this.preRollMs   = opts.preRollMs   ?? 120;
    this.postRollMs  = opts.postRollMs  ?? 160;
    this.minSpeechMs = opts.minSpeechMs ?? 200;
    this.maxSegmentMs= opts.maxSegmentMs?? 15000;

    this._xName = 'input';
    this._stateName = 'state';      // RNN状态输入
    this._srName = 'sr';
    this._probName = 'output';      // 概率输出
    this._stateOutName = 'stateN';  // 下一帧RNN状态输出

    this.ready = false;
    this.reset();
  }

  reset () {
    this.buf = Buffer.alloc(0);
    this.frames = [];        // {buf, speech}
    this.state = 'idle';     // idle | maybe_start | in_speech | maybe_end
    this.startCnt = 0;
    this.endCnt = 0;
    this.current = null;     // {s, e} 帧区间[含s,不含e)
    // RNN状态缓存（本模型是单一state维度 [2,1,128]）
    this._rnn = { state: null };
  }

  async init (modelPath = './models/silero_vad.onnx', useDirectML = false) {
    this.session = await ort.InferenceSession.create(modelPath, {
      executionProviders: useDirectML ? ['dml', 'cpu'] : ['cpu']
    });

    // 直接按 [2,1,128] 初始化为0（2层RNN，batch=1，隐藏128）
    const zeros = new Float32Array(2 * 1 * 128);
    this._rnn.state = new ort.Tensor('float32', zeros, [2, 1, 128]);

    // 可选：打印 I/O 以便排错
    // console.log('inputs:', this.session.inputNames, this.session.inputMetadata);
    // console.log('outputs:', this.session.outputNames, this.session.outputMetadata);

    this.ready = true;
  }

  /**
   * 写入一块 S16LE PCM（16k/mono），返回可能切出来的片段数组
   * @param {Buffer} pcmChunk
   * @returns {Array<{buffer:Buffer,startMs:number,endMs:number,durationMs:number,partial:boolean}>}
   */
  async write (pcmChunk) {
    if (!this.ready) return [];
    const emits = [];
    this.buf = Buffer.concat([this.buf, pcmChunk]);
    const frameBytes = this.frameLen * 2; // S16LE 2 bytes/sample

    while (this.buf.length >= frameBytes) {
      const frame = this.buf.subarray(0, frameBytes);
      this.buf = this.buf.subarray(frameBytes);

      // S16LE → Float32 [-1,1]
      const f32 = new Float32Array(this.frameLen);
      for (let i = 0; i < this.frameLen; i++) {
        f32[i] = Math.max(-1, Math.min(1, frame.readInt16LE(i * 2) / 32768));
      }
      const x = new ort.Tensor('float32', f32, [1, this.frameLen]);
      const sr = new ort.Tensor('int64', new BigInt64Array([BigInt(this.sampleRate)]), []);

      const feeds = {
        [this._xName]: x,
        [this._stateName]: this._rnn.state,
        [this._srName]: sr,
      };

      const out = await this.session.run(feeds);

      // 概率（通常 shape [1,1]，取 data[0] 即可）
      const probTensor = out[this._probName];
      const prob = probTensor?.data?.[0] ?? 0;
      const isSpeech = prob >= this.threshold;

      // 回填RNN状态
      this._rnn.state = out[this._stateOutName];

      // ====== 分段状态机 ======
      this.frames.push({ buf: frame, speech: isSpeech });

      if (this.state === 'idle') {
        if (isSpeech) { this.state = 'maybe_start'; this.startCnt = 1; }
      }
      else if (this.state === 'maybe_start') {
        if (isSpeech) {
          this.startCnt++;
          if (this.startCnt >= this.nStart) {
            const pre = Math.ceil(this.preRollMs / this.frameMs);
            const startIdx = Math.max(0, this.frames.length - this.startCnt - pre);
            this.current = { s: startIdx, e: this.frames.length };
            this.state = 'in_speech'; this.endCnt = 0;
          }
        } else {
          this.state = 'idle'; this.startCnt = 0;
        }
      }
      else if (this.state === 'in_speech') {
        if (isSpeech) {
          this.current.e = this.frames.length;
          this.endCnt = 0;
          if (this._curDurMs() >= this.maxSegmentMs) {
            // 强切中段
            const seg = this._finalize(true);
            if (seg) emits.push(seg);
            this.state = 'maybe_start'; this.startCnt = 0; this.endCnt = 0; this.current = null;
          }
        } else {
          this.state = 'maybe_end'; this.endCnt = 1;
        }
      }
      else if (this.state === 'maybe_end') {
        if (isSpeech) {
          this.state = 'in_speech'; this.current.e = this.frames.length; this.endCnt = 0;
        } else {
          this.endCnt++;
          if (this.endCnt >= this.nEnd) {
            const seg = this._finalize(false);
            if (seg) emits.push(seg);
            this.state = 'idle'; this.startCnt = 0; this.endCnt = 0; this.current = null;
          }
        }
      }
    }

    return emits;
  }

  end () {
    const emits = [];
    if (this.current) {
      const seg = this._finalize(false);
      if (seg) emits.push(seg);
    }
    this.reset();
    return emits;
  }

  _curDurMs () {
    if (!this.current) return 0;
    return (this.current.e - this.current.s) * this.frameMs;
  }

  _finalize (partial) {
    if (!this.current) return null;

    const post = Math.ceil(this.postRollMs / this.frameMs);
    const endIdx = Math.min(this.frames.length, this.current.e + post);
    const startIdx = this.current.s;
    const startMs = startIdx * this.frameMs;
    const endMs = endIdx * this.frameMs;
    const durMs = endMs - startMs;

    if (durMs < this.minSpeechMs) {
      // 丢弃极短段
      return null;
    }

    const chunk = Buffer.concat(this.frames.slice(startIdx, endIdx).map(f => f.buf));

    // 内存修剪：保留最近1s 帧用于下一段 preRoll
    const keepTail = Math.ceil(1000 / this.frameMs);
    const dropUntil = Math.max(0, endIdx - keepTail);
    this.frames = this.frames.slice(dropUntil);

    return { buffer: chunk, startMs, endMs, durationMs: durMs, partial: !!partial };
  }
}

module.exports = { SileroVAD };
