// whisper_asr.js
// 依赖：axios、form-data
// npm i axios form-data
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

/** 可选：把 Int16LE PCM 包成 WAV（若你已有同名工具，可删掉这段并改为 require('./pcm2wav')） */
function pcmToWav (pcmBuffer, {
    sampleRate = 16000,
    channels = 1,
    bitsPerSample = 16,
} = {}) {
    const byteRate = sampleRate * channels * bitsPerSample / 8;
    const blockAlign = channels * bitsPerSample / 8;
    const dataSize = pcmBuffer.length;
    const headerSize = 44;
    const buffer = Buffer.alloc(headerSize + dataSize);

    buffer.write('RIFF', 0);
    buffer.writeUInt32LE(36 + dataSize, 4);
    buffer.write('WAVE', 8);
    buffer.write('fmt ', 12);
    buffer.writeUInt32LE(16, 16);
    buffer.writeUInt16LE(1, 20);
    buffer.writeUInt16LE(channels, 22);
    buffer.writeUInt32LE(sampleRate, 24);
    buffer.writeUInt32LE(byteRate, 28);
    buffer.writeUInt16LE(blockAlign, 32);
    buffer.writeUInt16LE(bitsPerSample, 34);
    buffer.write('data', 36);
    buffer.writeUInt32LE(dataSize, 40);
    pcmBuffer.copy(buffer, 44);
    return buffer;
}

/**
 * 调用本地 Whisper ASR 服务
 * 对应接口：POST /asr?encode=true&task=transcribe&vad_filter=false&word_timestamps=false&output=json&language=zh
 *
 * @param {Object} opts
 * 输入三选一：
 *  - filePath: string
 *  - buffer: Buffer
 *  - stream: Readable
 *  - lang?: string = 'zh'
 *
 * PCM 相关（仅当传入的是“裸 PCM”时需要）：
 *  - pcm: { sampleRate?:number, channels?:number, bitsPerSample?:number }  // 若提供则自动包 WAV
 *
 * 其他参数：
 *  - filename?: string = 'audio.wav'
 *  - mimeType?: string = 'audio/wav'     // 传 WAV 就保持默认
 *  - baseURL?: string = 'http://127.0.0.1:9000'
 *  - query?: { encode?:boolean, task?:'transcribe'|'translate', vad_filter?:boolean, word_timestamps?:boolean, output?:'json' }
 *  - timeoutMs?: number = 60000
 *  - debug?: boolean = false
 *
 * @returns {Promise<{ language:string, text:string, segments:Array, raw:any }>}
 */
async function asrTranscribe (opts = {}) {
    try {
        const {
        filePath,
        buffer,
        stream,
        pcm, // 若传入裸PCM，就提供 {sampleRate, channels, bitsPerSample} 触发自动包头
        filename = 'audio.wav',
        mimeType = 'audio/wav',
        baseURL = 'http://106.55.150.116:7777',
        query = {
            encode: true,
            task: 'transcribe',
            vad_filter: true,
            word_timestamps: false,
            output: 'json',
        },
        timeoutMs = 60_000,
        debug = false,
        lang = '',
    } = opts;

    const provided = [filePath, buffer, stream].filter(Boolean).length;
    if (provided !== 1) throw new Error('必须且只能提供 filePath / buffer / stream 其中一种');

    // 组装 URL 与查询参数
    const url = new URL('/asr', baseURL);
    const qp = {
        encode: query.encode !== false, // 默认 true
        task: query.task || 'transcribe',
        vad_filter: !!query.vad_filter,
        word_timestamps: !!query.word_timestamps,
        output: query.output || 'json',
    };
    if (lang) {
        qp.language = lang;
    }
    Object.entries(qp).forEach(([k, v]) => url.searchParams.set(k, String(v)));

    // 构造 multipart
    const form = new FormData();
    if (filePath) {
        const real = path.resolve(filePath);
        if (!fs.existsSync(real)) throw new Error(`文件不存在：${real}`);
        form.append('audio_file', fs.createReadStream(real), {
            filename: path.basename(real),
            contentType: mimeType,
        });
    } else if (buffer) {
        const payload = pcm ? pcmToWav(buffer, pcm) : buffer; // 如为裸PCM则包头
        form.append('audio_file', payload, { filename, contentType: mimeType });
    } else {
        // Readable 流：若是 PCM 流，建议你在外部先拼满一段后转 WAV 再传
        form.append('audio_file', stream, { filename, contentType: mimeType });
    }

    if (debug) {
        console.log('[whisper_asr] POST', url.toString());
    }

    const instance = axios.create({
        proxy: false,
        timeout: timeoutMs,
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        validateStatus: () => true, // 统一自己处理 4xx/5xx
        headers: { accept: 'application/json', ...form.getHeaders() },
    });

    // HTTP 请求重试机制
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            if (debug && attempt > 1) {
                console.log(`[whisper_asr] 重试第 ${attempt - 1} 次...`);
            }

            const resp = await instance.post(url.toString(), form);

            // 检查 HTTP 状态码
            if (resp.status >= 400) {
                const brief = typeof resp.data === 'string' ? resp.data.slice(0, 500) : JSON.stringify(resp.data);
                const server = resp.headers?.server || '';
                const errorMsg = `ASR 接口返回错误（HTTP ${resp.status}${server ? ', server=' + server : ''}）：${brief}`;

                // 如果是客户端错误（4xx），不重试
                if (resp.status >= 400 && resp.status < 500) {
                    throw new Error(errorMsg);
                }

                // 服务器错误（5xx）或其他错误，记录错误并准备重试
                lastError = new Error(errorMsg);
                if (debug) {
                    console.log(`[whisper_asr] 第 ${attempt} 次请求失败：${errorMsg}`);
                }

                // 如果是最后一次尝试，抛出错误
                if (attempt === maxRetries) {
                    throw lastError;
                }

                // 等待一段时间后重试（指数退避）
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                if (debug) {
                    console.log(`[whisper_asr] 等待 ${delay}ms 后重试...`);
                }
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
            }

            // 请求成功，处理响应数据
            const data = resp.data || {};
            // 统一返回结构
            return {
                language: data.language ?? '',
                text: data.text ?? '',
                segments: Array.isArray(data.segments) ? data.segments : [],
                raw: data,
            };

        } catch (error) {
            lastError = error;

            // 如果是网络错误、超时等，可以重试
            const isRetryableError = (
                error.code === 'ECONNREFUSED' ||
                error.code === 'ENOTFOUND' ||
                error.code === 'ETIMEDOUT' ||
                error.code === 'ECONNRESET' ||
                error.code === 'ECONNABORTED' ||  // 添加超时中断错误
                error.message.includes('timeout') ||
                error.message.includes('Network Error') ||
                error.message.includes('exceeded')  // 添加超时相关错误
            );

            if (debug) {
                console.log(`[whisper_asr] 第 ${attempt} 次请求出错：${error.message}`);
            }

            // 如果不是可重试的错误，或者是最后一次尝试，直接抛出错误
            if (!isRetryableError || attempt === maxRetries) {
                throw error;
            }

            // 等待一段时间后重试（指数退避）
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
            if (debug) {
                console.log(`[whisper_asr] 等待 ${delay}ms 后重试...`);
            }
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

        // 理论上不会到达这里，但为了安全起见
        throw lastError || new Error('HTTP 请求失败，已达到最大重试次数');

    } catch (error) {
        // 最外层错误捕获，确保不会有未处理的异常
        console.error('[whisper_asr] 未预期的错误:', error.message);

        // 返回一个安全的默认结果，而不是让进程崩溃
        return {
            language: '',
            text: '',
            segments: [],
            raw: { error: error.message },
            success: false,
            error: error.message
        };
    }
}

/**
 * 安全包装器，确保即使在最坏情况下也不会让进程崩溃
 */
async function safeAsrTranscribe(opts = {}) {
  try {
    return await asrTranscribe(opts);
  } catch (error) {
    console.error('[safeAsrTranscribe] 捕获到未处理的异常:', error);

    // 返回安全的默认结果
    return {
      language: '',
      text: '',
      segments: [],
      raw: { error: error.message },
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  asrTranscribe,
  safeAsrTranscribe,  // 推荐使用这个安全版本
  pcmToWav
};
