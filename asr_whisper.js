// asr_whisper_streaming_silero.js
// 依赖：./vad_silero.js（我给你的最新版，已实现 tick() ）
// 注意：输入必须是 16k / mono / 16-bit S16LE PCM

const { SileroVAD } = require('./vad_silero');
const { asrTranscribe } = require('./whisper_asr_local'); // 你本地 Whisper 封装
// 或者用远端：const { recognizeAsrWithWhisper } = require('./whisper_asr');

class AsrClient {
  /**
   * @param {string} lang                语言代码（'zh'等）
   * @param {number} tickIntervalMs      看门狗轮询间隔（默认200ms）
   */
  constructor (lang = 'zh', tickIntervalMs = 200) {
    this.lang = lang;
    this.cb = () => {};
    this.vadReady = false;
    this._destroyed = false;

    // 配好与 vad_silero.js 一致的参数
    this.vad = new SileroVAD({
      frameMs: 30,
      threshold: 0.55,   // 稍微抬一点更稳
      startMs: 60,
      endMs: 250,
      preRollMs: 120,
      postRollMs: 200,
      minSpeechMs: 200,
      maxSegmentMs: 15000,
      rmsSilence: 0.008,
      hardFlushMs: 1500,
      idleTimeoutMs: 2000,
    });

    // 初始化 ONNX 模型（原生优先，装不上会走 WASM）
    this.vad.init('./models/silero_vad.onnx' /*, useDirectML = false */)
      .then(() => { this.vadReady = true; })
      .catch((e) => { console.error('[VAD init error]', e); });

    // ====== 看门狗：定时触发 VAD 的 tick()，把卡住的尾段强制吐出 ======
    this._tickIntervalMs = tickIntervalMs;
    this._watch = setInterval(async () => {
      try {
        if (!this.vadReady || this._destroyed) return;
        const seg = this.vad.tick && this.vad.tick();
        if (!seg) return;
        const res = await asrTranscribe({
          buffer: seg.buffer,
          pcm: { sampleRate: 16000, channels: 1, bitsPerSample: 16 },
          filename: 'chunk.wav',
          lang: this.lang,
        });
        this.cb({ transcript: res.text || '', isFinal: true });
      } catch (err) {
        this.cb({ transcript: '', isFinal: true, error: String(err) });
      }
    }, this._tickIntervalMs);
  }

  /**
   * 持续喂入音频（16k/mono/S16LE），分段后自动识别并回调
   * @param {Buffer} audioData
   * @param {(ret:{transcript:string,isFinal:boolean,error?:string})=>void} callback
   * @param {string} lang
   */
  async requestAsr (audioData, callback = () => {}, lang = '') {
    if (lang) this.lang = lang;
    if (callback) this.cb = callback;
    if (!this.vadReady || this._destroyed) return;

    try {
      const segments = await this.vad.write(audioData);
      for (const seg of segments) {
        const res = await asrTranscribe({
          buffer: seg.buffer,
          pcm: { sampleRate: 16000, channels: 1, bitsPerSample: 16 },
          filename: 'chunk.wav',
          lang: this.lang,
        });
        this.cb({ transcript: res.text || '', isFinal: !seg.partial });
      }
    } catch (err) {
      this.cb({ transcript: '', isFinal: true, error: String(err) });
    }
  }

  /**
   * 主动结束当前会话（比如用户手动停止）
   * 会把未吐出的尾段 finalize 一次
   */
  end () {
    if (!this.vadReady || this._destroyed) return;
    const segments = this.vad.end();
    segments.forEach(async (seg) => {
      try {
        const res = await asrTranscribe({
          buffer: seg.buffer,
          pcm: { sampleRate: 16000, channels: 1, bitsPerSample: 16 },
          filename: 'chunk.wav',
          lang: this.lang,
        });
        this.cb({ transcript: res.text || '', isFinal: true });
      } catch (err) {
        this.cb({ transcript: '', isFinal: true, error: String(err) });
      }
    });
  }

  /**
   * 资源清理：关闭看门狗，标记销毁
   */
  destroy () {
    this._destroyed = true;
    if (this._watch) clearInterval(this._watch);
  }
}

module.exports = { AsrClient };
