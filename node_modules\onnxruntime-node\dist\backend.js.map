{"version": 3, "file": "backend.js", "sourceRoot": "", "sources": ["../lib/backend.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;AAIlC,uCAAsD;AAEtD,MAAM,eAAe,GAAG;IACtB,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;CACE,CAAC;AAEX,MAAM,yBAAyB;IAG7B,YAAY,YAAiC,EAAE,OAAwC;QAFvF,8DAA4C;QAG1C,IAAA,iBAAO,GAAE,CAAC;QAEV,uBAAA,IAAI,+CAAqB,IAAI,iBAAO,CAAC,gBAAgB,EAAE,MAAA,CAAC;QACxD,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YACpC,uBAAA,IAAI,mDAAkB,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SACzD;aAAM;YACL,uBAAA,IAAI,mDAAkB,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAClH;QAED,0CAA0C;QAC1C,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,8FAA8F;QAC9F,0CAA0C;QAC1C,yDAAyD;QACzD,MAAM,oBAAoB,GAAG,CAC3B,WAA6C,EACkB,EAAE;YACjE,MAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAqC,EAAE,CAAC;YAEtD,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE;gBAC3B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACnB,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;oBACf,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;iBAClD;qBAAM;oBACL,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBACrC,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;qBACrD;oBACD,MAAM,KAAK,GAA2B,EAAE,CAAC;oBACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;wBACvC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACvB,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;4BACd,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;yBACrC;6BAAM,IAAI,GAAG,IAAI,CAAC,EAAE;4BACnB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACjB;6BAAM;4BACL,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;yBAC9C;qBACF;oBACD,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;wBACZ,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,IAAI;wBACJ,KAAK;qBACN,CAAC,CAAC;iBACJ;aACF;YAED,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3B,CAAC,CAAC;QAEF,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,oBAAoB,CAAC,uBAAA,IAAI,mDAAkB,CAAC,aAAa,CAAC,CAAC;QACnG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,oBAAoB,CAAC,uBAAA,IAAI,mDAAkB,CAAC,cAAc,CAAC,CAAC;IACxG,CAAC;IAED,KAAK,CAAC,OAAO;QACX,uBAAA,IAAI,mDAAkB,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAQD,cAAc;QACZ,6BAA6B;QAC7B,EAAE;QACF,iGAAiG;IACnG,CAAC;IACD,YAAY;QACV,uBAAA,IAAI,mDAAkB,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,GAAG,CACP,KAA+B,EAC/B,OAAmC,EACnC,OAAoC;QAEpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,YAAY,CAAC,GAAG,EAAE;gBAChB,IAAI;oBACF,OAAO,CAAC,uBAAA,IAAI,mDAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;iBAC9D;gBAAC,OAAO,CAAC,EAAE;oBACV,gCAAgC;oBAChC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;;AAED,MAAM,kBAAkB;IACtB,KAAK,CAAC,IAAI;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,6BAA6B,CACjC,YAAiC,EACjC,OAAyC;QAEzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,YAAY,CAAC,GAAG,EAAE;gBAChB,IAAI;oBACF,OAAO,CAAC,IAAI,yBAAyB,CAAC,YAAY,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;iBACrE;gBAAC,OAAO,CAAC,EAAE;oBACV,gCAAgC;oBAChC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAC9C,QAAA,qBAAqB,GAAG,iBAAO,CAAC,qBAAqB,CAAC"}